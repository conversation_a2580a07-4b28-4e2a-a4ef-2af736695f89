# Production Environment
ENVIRONMENT=prod

# Server Configuration
PORT=8080
GIN_MODE=release
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=60

# Database Configuration
DB_DRIVER=postgres
DB_HOST=prod-db.example.com
DB_PORT=5432
DB_USER=prod_user
DB_PASSWORD=your_production_password_here
DB_NAME=tasks_prod
DB_SSLMODE=require
DB_MAX_CONNECTIONS=100
DB_CONNECTION_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_ENABLE_SQL=false

# Security Configuration
CORS_ORIGINS=https://example.com,https://www.example.com
RATE_LIMIT=50
ENABLE_HTTPS=true
