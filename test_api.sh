#!/bin/bash

# Task Management Service API Test Script
# This script tests all the API endpoints to demonstrate the functionality

BASE_URL="http://localhost:8080/api/v1"
echo "🧪 Testing Task Management Service API"
echo "======================================"

# Test 1: Health Check
echo -e "\n1️⃣ Testing Health Check"
echo "GET /health"
response=$(curl -s http://localhost:8080/health)
echo "Response: $response"

# Test 2: Get All Tasks (empty initially)
echo -e "\n2️⃣ Testing Get All Tasks (initially empty)"
echo "GET /tasks"
response=$(curl -s "$BASE_URL/tasks")
echo "Response: $response"

# Test 3: Create First Task
echo -e "\n3️⃣ Testing Create Task"
echo "POST /tasks"
task1_response=$(curl -s -X POST "$BASE_URL/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Complete Project Documentation",
    "description": "Write comprehensive documentation for the microservices project",
    "priority": 3,
    "status": "Pending"
  }')
echo "Response: $task1_response"

# Extract task ID from response
task1_id=$(echo $task1_response | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
echo "Created Task ID: $task1_id"

# Test 4: Create Second Task
echo -e "\n4️⃣ Testing Create Second Task"
echo "POST /tasks"
task2_response=$(curl -s -X POST "$BASE_URL/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Implement Authentication",
    "description": "Add JWT authentication to the service",
    "priority": 5,
    "status": "InProgress"
  }')
echo "Response: $task2_response"

# Test 5: Create Third Task
echo -e "\n5️⃣ Testing Create Third Task"
echo "POST /tasks"
task3_response=$(curl -s -X POST "$BASE_URL/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Write Unit Tests",
    "description": "Create comprehensive unit tests for all components",
    "priority": 2,
    "status": "Completed"
  }')
echo "Response: $task3_response"

# Test 6: Get All Tasks (with data)
echo -e "\n6️⃣ Testing Get All Tasks (with data)"
echo "GET /tasks"
response=$(curl -s "$BASE_URL/tasks")
echo "Response: $response"

# Test 7: Get Tasks with Pagination
echo -e "\n7️⃣ Testing Get Tasks with Pagination"
echo "GET /tasks?page=1&page_size=2"
response=$(curl -s "$BASE_URL/tasks?page=1&page_size=2")
echo "Response: $response"

# Test 8: Get Tasks with Status Filter
echo -e "\n8️⃣ Testing Get Tasks with Status Filter"
echo "GET /tasks?status=Completed"
response=$(curl -s "$BASE_URL/tasks?status=Completed")
echo "Response: $response"

# Test 9: Get Tasks with Priority Filter
echo -e "\n9️⃣ Testing Get Tasks with Priority Filter"
echo "GET /tasks?priority=5"
response=$(curl -s "$BASE_URL/tasks?priority=5")
echo "Response: $response"

# Test 10: Get Task by ID
echo -e "\n🔟 Testing Get Task by ID"
echo "GET /tasks/$task1_id"
response=$(curl -s "$BASE_URL/tasks/$task1_id")
echo "Response: $response"

# Test 11: Update Task
echo -e "\n1️⃣1️⃣ Testing Update Task"
echo "PUT /tasks/$task1_id"
response=$(curl -s -X PUT "$BASE_URL/tasks/$task1_id" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Complete Project Documentation (Updated)",
    "status": "InProgress",
    "priority": 4
  }')
echo "Response: $response"

# Test 12: Get Updated Task
echo -e "\n1️⃣2️⃣ Testing Get Updated Task"
echo "GET /tasks/$task1_id"
response=$(curl -s "$BASE_URL/tasks/$task1_id")
echo "Response: $response"

# Test 13: Test Invalid Task ID
echo -e "\n1️⃣3️⃣ Testing Invalid Task ID"
echo "GET /tasks/invalid-uuid"
response=$(curl -s "$BASE_URL/tasks/invalid-uuid")
echo "Response: $response"

# Test 14: Test Non-existent Task ID
echo -e "\n1️⃣4️⃣ Testing Non-existent Task ID"
echo "GET /tasks/550e8400-e29b-41d4-a716-************"
response=$(curl -s "$BASE_URL/tasks/550e8400-e29b-41d4-a716-************")
echo "Response: $response"

# Test 15: Delete Task
echo -e "\n1️⃣5️⃣ Testing Delete Task"
echo "DELETE /tasks/$task1_id"
response=$(curl -s -X DELETE "$BASE_URL/tasks/$task1_id")
echo "Response: $response"

# Test 16: Verify Task Deleted
echo -e "\n1️⃣6️⃣ Testing Verify Task Deleted"
echo "GET /tasks/$task1_id"
response=$(curl -s "$BASE_URL/tasks/$task1_id")
echo "Response: $response"

# Test 17: Final Get All Tasks
echo -e "\n1️⃣7️⃣ Testing Final Get All Tasks"
echo "GET /tasks"
response=$(curl -s "$BASE_URL/tasks")
echo "Response: $response"

echo -e "\n✅ API Testing Complete!"
echo "======================================"

