version: '3.8'

services:
  task-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=debug
      - DB_DRIVER=sqlite
      - DB_NAME=tasks.db
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Example of how you might add a User Service in the future
  # user-service:
  #   build: ./user-service
  #   ports:
  #     - "8081:8080"
  #   environment:
  #     - PORT=8080
  #     - DB_DRIVER=postgres
  #     - DB_HOST=postgres
  #     - DB_PORT=5432
  #     - DB_USER=postgres
  #     - DB_PASSWORD=password
  #     - DB_NAME=users
  #   depends_on:
  #     - postgres
  #   restart: unless-stopped

  # Example database service for production
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     - POSTGRES_USER=postgres
  #     - POSTGRES_PASSWORD=password
  #     - POSTGRES_DB=tasks
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped

volumes:
  postgres_data:

