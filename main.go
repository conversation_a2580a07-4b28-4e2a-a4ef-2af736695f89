package main

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/pvnptl/alle-task-service/internal/config"
	"github.com/pvnptl/alle-task-service/internal/database"
	"github.com/pvnptl/alle-task-service/internal/handlers"
	"github.com/pvnptl/alle-task-service/internal/middleware"
	"github.com/pvnptl/alle-task-service/internal/repository"
	"github.com/pvnptl/alle-task-service/internal/services"
)

// main is the entry point of the Task Management Service.
// It demonstrates the dependency injection pattern and clean architecture principles.
// The application follows a layered architecture: Handlers → Services → Repository → Database

func main() {
	// Load environment variables from .env file if it exists
	// This allows for flexible configuration management across different environments
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using default configuration")
	}

	// Initialize configuration with environment-specific settings
	// This centralizes all configuration management
	cfg := config.New()

	// Log environment information
	log.Printf("Starting Task Management Service in %s environment", cfg.Environment)
	log.Printf("Server Mode: %s", cfg.Server.Mode)
	log.Printf("Database Driver: %s", cfg.Database.Driver)
	log.Printf("Log Level: %s", cfg.Logging.Level)

	// Initialize database connection with the provided configuration
	// The database layer is abstracted to allow easy switching between different databases
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize repository layer - responsible for data access operations
	// This follows the Repository pattern for data access abstraction
	taskRepo := repository.NewTaskRepository(db)

	// Initialize service layer - contains business logic
	// Services depend on repositories through interfaces, enabling easy testing and mocking
	taskService := services.NewTaskService(taskRepo)

	// Initialize HTTP handlers - responsible for request/response handling
	// Handlers depend on services and handle HTTP-specific concerns
	taskHandler := handlers.NewTaskHandler(taskService)

	// Setup HTTP router with middleware and route definitions
	// This configures the web server with proper middleware and API endpoints
	router := setupRouter(taskHandler, cfg)

	log.Printf("Starting Task Management Service on port %s", cfg.Server.Port)
	if err := router.Run(":" + cfg.Server.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// setupRouter configures the HTTP router with middleware and API routes.
// This function demonstrates proper middleware ordering and RESTful API design.
func setupRouter(taskHandler *handlers.TaskHandler, cfg *config.Config) *gin.Engine {
	// Set Gin mode based on configuration
	gin.SetMode(cfg.Server.Mode)

	router := gin.Default()

	// Add middleware in the correct order
	// CORS should be first to handle preflight requests
	router.Use(middleware.CORS())
	// Logger middleware for request tracking
	router.Use(middleware.Logger())
	// Recovery middleware to handle panics gracefully
	router.Use(middleware.Recovery())

	// Health check endpoint for monitoring and load balancers
	// This is essential for container orchestration and service discovery
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":      "healthy",
			"service":     "task-management-service",
			"environment": string(cfg.Environment),
			"version":     "1.0.0",
		})
	})

	// API routes with versioning for backward compatibility
	// Versioning allows for API evolution without breaking existing clients
	api := router.Group("/api/v1")
	{
		// Task-related endpoints following RESTful conventions
		tasks := api.Group("/tasks")
		{
			// GET /tasks - List tasks with pagination and filtering
			tasks.GET("", taskHandler.GetTasks)
			// GET /tasks/:id - Get a specific task by ID
			tasks.GET("/:id", taskHandler.GetTaskByID)
			// POST /tasks - Create a new task
			tasks.POST("", taskHandler.CreateTask)
			// PUT /tasks/:id - Update an existing task
			tasks.PUT("/:id", taskHandler.UpdateTask)
			// DELETE /tasks/:id - Delete a task
			tasks.DELETE("/:id", taskHandler.DeleteTask)
		}
	}

	return router
}
