{"info": {"_postman_id": "alle-task-service-collection", "name": "Task Management Service API", "description": "Complete API collection for the Task Management Microservice built with Go and Gin. This collection includes all CRUD operations, pagination, filtering, and error handling examples.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "alle-task-service"}, "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('status');", "    pm.expect(response).to.have.property('service');", "    pm.expect(response.status).to.eql('healthy');", "    pm.expect(response.service).to.eql('task-management-service');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the service is healthy and running. This endpoint is useful for monitoring and load balancer health checks."}, "response": []}, {"name": "Get All Tasks", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('tasks');", "    pm.expect(response).to.have.property('pagination');", "    pm.expect(response.tasks).to.be.an('array');", "    pm.expect(response.pagination).to.have.property('page');", "    pm.expect(response.pagination).to.have.property('page_size');", "    pm.expect(response.pagination).to.have.property('total');", "    pm.expect(response.pagination).to.have.property('total_pages');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "page_size", "value": "10", "description": "Number of items per page (default: 10, max: 100)"}]}, "description": "Retrieve all tasks with pagination. Supports filtering by status and priority."}, "response": []}, {"name": "Get Tasks with Status Filter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All returned tasks have the correct status\", function () {", "    const response = pm.response.json();", "    const status = pm.request.url.query.get('status');", "    response.tasks.forEach(task => {", "        pm.expect(task.status).to.eql(status);", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks?status=Completed", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"], "query": [{"key": "status", "value": "Completed", "description": "Filter by task status (Pending, InProgress, Completed, Cancelled)"}]}, "description": "Get tasks filtered by status. Valid statuses: Pending, InProgress, Completed, Cancelled."}, "response": []}, {"name": "Get Tasks with Priority Filter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All returned tasks have the correct priority\", function () {", "    const response = pm.response.json();", "    const priority = parseInt(pm.request.url.query.get('priority'));", "    response.tasks.forEach(task => {", "        pm.expect(task.priority).to.eql(priority);", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks?priority=3", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"], "query": [{"key": "priority", "value": "3", "description": "Filter by priority level (1-5)"}]}, "description": "Get tasks filtered by priority level. Priority ranges from 1 (lowest) to 5 (highest)."}, "response": []}, {"name": "Create Task - Basic", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('title');", "    pm.expect(response).to.have.property('description');", "    pm.expect(response).to.have.property('status');", "    pm.expect(response).to.have.property('priority');", "    pm.expect(response).to.have.property('created_at');", "    pm.expect(response).to.have.property('updated_at');", "});", "", "pm.test(\"Task has default status\", function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('Pending');", "});", "", "// Store the task ID for other requests", "if (pm.response.json().id) {", "    pm.collectionVariables.set(\"task_id\", pm.response.json().id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Complete Project Documentation\",\n    \"description\": \"Write comprehensive documentation for the microservices project\",\n    \"priority\": 3\n}"}, "url": {"raw": "{{base_url}}/api/v1/tasks", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"]}, "description": "Create a new task with basic information. Status will default to 'Pending' if not provided."}, "response": []}, {"name": "Create Task - Complete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Task has specified status\", function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('InProgress');", "});", "", "pm.test(\"Task has specified priority\", function () {", "    const response = pm.response.json();", "    pm.expect(response.priority).to.eql(5);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Implement Authentication\",\n    \"description\": \"Add JWT authentication to the service\",\n    \"status\": \"InProgress\",\n    \"priority\": 5,\n    \"due_date\": \"2024-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/tasks", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"]}, "description": "Create a task with all fields specified including status, priority, and due date."}, "response": []}, {"name": "Get Task by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('title');", "    pm.expect(response).to.have.property('description');", "    pm.expect(response).to.have.property('status');", "    pm.expect(response).to.have.property('priority');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks/{{task_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks", "{{task_id}}"]}, "description": "Retrieve a specific task by its UUID. This endpoint will return 404 if the task doesn't exist."}, "response": []}, {"name": "Update Task - Partial", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Task was updated correctly\", function () {", "    const response = pm.response.json();", "    pm.expect(response.title).to.eql('Updated Task Title');", "    pm.expect(response.status).to.eql('InProgress');", "    pm.expect(response.priority).to.eql(4);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Task Title\",\n    \"status\": \"InProgress\",\n    \"priority\": 4\n}"}, "url": {"raw": "{{base_url}}/api/v1/tasks/{{task_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks", "{{task_id}}"]}, "description": "Update specific fields of an existing task. Only the fields you want to change need to be included."}, "response": []}, {"name": "Update Task - Complete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All fields were updated\", function () {", "    const response = pm.response.json();", "    pm.expect(response.title).to.eql('Fully Updated Task');", "    pm.expect(response.description).to.eql('This is a completely updated task');", "    pm.expect(response.status).to.eql('Completed');", "    pm.expect(response.priority).to.eql(1);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Fully Updated Task\",\n    \"description\": \"This is a completely updated task\",\n    \"status\": \"Completed\",\n    \"priority\": 1,\n    \"due_date\": \"2024-06-30T12:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/tasks/{{task_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks", "{{task_id}}"]}, "description": "Update all fields of an existing task. This demonstrates a complete task update."}, "response": []}, {"name": "Delete Task", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success message\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('message');", "    pm.expect(response.message).to.eql('Task deleted successfully');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks/{{task_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks", "{{task_id}}"]}, "description": "Delete a task by its UUID. This operation is irreversible."}, "response": []}, {"name": "<PERSON><PERSON><PERSON> - Invalid UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Error message is correct\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('error');", "    pm.expect(response.error).to.eql('Invalid task ID');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks/invalid-uuid", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks", "invalid-uuid"]}, "description": "Test error handling for invalid UUID format. Should return 400 Bad Request."}, "response": []}, {"name": "Error <PERSON> - Task Not Found", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Error message is correct\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('error');", "    pm.expect(response.error).to.eql('Task not found');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks/550e8400-e29b-41d4-a716-************", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks", "550e8400-e29b-41d4-a716-************"]}, "description": "Test error handling for non-existent task. Should return 404 Not Found."}, "response": []}, {"name": "Error <PERSON> - Invalid Request Body", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Error message is present\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('error');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"Task without title should fail\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/tasks", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"]}, "description": "Test validation error when required field (title) is missing. Should return 400 Bad Request."}, "response": []}, {"name": "Pagination - Page 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Pagination metadata is correct\", function () {", "    const response = pm.response.json();", "    pm.expect(response.pagination.page).to.eql(2);", "    pm.expect(response.pagination.page_size).to.eql(5);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/tasks?page=2&page_size=5", "host": ["{{base_url}}"], "path": ["api", "v1", "tasks"], "query": [{"key": "page", "value": "2", "description": "Get second page of results"}, {"key": "page_size", "value": "5", "description": "5 items per page"}]}, "description": "Test pagination by requesting the second page with 5 items per page."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "task_id", "value": "", "type": "string"}]}