#!/bin/bash

# Script to run the Task Management Service in different environments
# Usage: ./scripts/run-env.sh [local|test|staging|prod]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Task Management Service${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Default environment
ENVIRONMENT=${1:-local}

# Validate environment
case $ENVIRONMENT in
    local|test|staging|prod)
        print_status "Using environment: $ENVIRONMENT"
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        echo "Valid environments: local, test, staging, prod"
        exit 1
        ;;
esac

# Check if environment file exists
ENV_FILE="env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    print_error "Environment file $ENV_FILE not found!"
    echo "Available environment files:"
    ls -1 env.* 2>/dev/null || echo "No environment files found"
    exit 1
fi

print_header
print_status "Starting Task Management Service in $ENVIRONMENT environment"

# Copy environment file to .env
cp "$ENV_FILE" .env
print_status "Loaded configuration from $ENV_FILE"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

# Check if dependencies are installed
if [ ! -f "go.sum" ]; then
    print_warning "Dependencies not installed, running go mod tidy..."
    go mod tidy
fi

# Run the application
print_status "Starting server..."
print_status "Environment: $ENVIRONMENT"
print_status "Configuration: $ENV_FILE"

# Set environment variable for Go
export ENVIRONMENT=$ENVIRONMENT

# Run the application
go run main.go
