# Local Development Environment
ENVIRONMENT=local

# Server Configuration
PORT=8080
GIN_MODE=debug
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=60

# Database Configuration
DB_DRIVER=sqlite
DB_HOST=localhost
DB_PORT=5432
DB_USER=
DB_PASSWORD=
DB_NAME=tasks.db
DB_SSLMODE=disable
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=text
LOG_OUTPUT=stdout
LOG_ENABLE_SQL=true

# Security Configuration
CORS_ORIGINS=*
RATE_LIMIT=1000
ENABLE_HTTPS=false
