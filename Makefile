.PHONY: build run test clean deps lint

# Build the application
build:
	go build -o bin/task-service main.go

# Run the application
run:
	go run main.go

# Run in different environments
run-local:
	./scripts/run-env.sh local

run-test:
	./scripts/run-env.sh test

run-staging:
	./scripts/run-env.sh staging

run-prod:
	./scripts/run-env.sh prod

# Run tests
test:
	go test -v ./...

# Run tests with coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out

# Clean build artifacts
clean:
	rm -rf bin/
	rm -f coverage.out
	rm -f *.db

# Install dependencies
deps:
	go mod download
	go mod tidy

# Lint the code
lint:
	golangci-lint run

# Run the application in development mode
dev:
	gin --appPort 8080 --port 3000 run main.go

# Build for production
build-prod:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/task-service main.go

# Docker build
docker-build:
	docker build -t task-service .

# Docker run
docker-run:
	docker run -p 8080:8080 task-service

