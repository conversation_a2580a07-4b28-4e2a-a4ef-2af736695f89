package services

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/pvnptl/alle-task-service/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockTaskRepository is a mock implementation of TaskRepository
type MockTaskRepository struct {
	mock.Mock
}

func (m *MockTaskRepository) Create(task *models.Task) error {
	args := m.Called(task)
	return args.Error(0)
}

func (m *MockTaskRepository) GetByID(id uuid.UUID) (*models.Task, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Task), args.Error(1)
}

func (m *MockTaskRepository) GetAll(filter *models.TaskFilter) ([]models.Task, int64, error) {
	args := m.Called(filter)
	return args.Get(0).([]models.Task), args.Get(1).(int64), args.Error(2)
}

func (m *MockTaskRepository) Update(task *models.Task) error {
	args := m.Called(task)
	return args.Error(0)
}

func (m *MockTaskRepository) Delete(id uuid.UUID) error {
	args := m.Called(id)
	return args.Error(0)
}

func TestTaskService_CreateTask(t *testing.T) {
	mockRepo := new(MockTaskRepository)
	service := NewTaskService(mockRepo)

	tests := []struct {
		name    string
		req     *models.CreateTaskRequest
		setup   func()
		wantErr bool
	}{
		{
			name: "successful task creation",
			req: &models.CreateTaskRequest{
				Title:       "Test Task",
				Description: "Test Description",
				Priority:    3,
			},
			setup: func() {
				mockRepo.On("Create", mock.AnythingOfType("*models.Task")).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "invalid status",
			req: &models.CreateTaskRequest{
				Title:       "Test Task",
				Description: "Test Description",
				Status:      "InvalidStatus",
				Priority:    3,
			},
			setup:   func() {},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()

			result, err := service.CreateTask(tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.req.Title, result.Title)
				assert.Equal(t, tt.req.Description, result.Description)
				assert.Equal(t, tt.req.Priority, result.Priority)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestTaskService_GetTaskByID(t *testing.T) {
	mockRepo := new(MockTaskRepository)
	service := NewTaskService(mockRepo)

	taskID := uuid.New()
	expectedTask := &models.Task{
		ID:          taskID,
		Title:       "Test Task",
		Description: "Test Description",
		Status:      models.StatusPending,
		Priority:    3,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	mockRepo.On("GetByID", taskID).Return(expectedTask, nil)

	result, err := service.GetTaskByID(taskID)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedTask.ID, result.ID)
	assert.Equal(t, expectedTask.Title, result.Title)

	mockRepo.AssertExpectations(t)
}

func TestTaskService_GetTasks(t *testing.T) {
	mockRepo := new(MockTaskRepository)
	service := NewTaskService(mockRepo)

	filter := &models.TaskFilter{
		Page:     1,
		PageSize: 10,
	}

	expectedTasks := []models.Task{
		{
			ID:          uuid.New(),
			Title:       "Task 1",
			Description: "Description 1",
			Status:      models.StatusPending,
			Priority:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New(),
			Title:       "Task 2",
			Description: "Description 2",
			Status:      models.StatusCompleted,
			Priority:    2,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	mockRepo.On("GetAll", filter).Return(expectedTasks, int64(2), nil)

	result, err := service.GetTasks(filter)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Tasks, 2)
	assert.Equal(t, 1, result.Pagination.Page)
	assert.Equal(t, 10, result.Pagination.PageSize)
	assert.Equal(t, 2, result.Pagination.Total)

	mockRepo.AssertExpectations(t)
}

func TestTaskService_UpdateTask(t *testing.T) {
	mockRepo := new(MockTaskRepository)
	service := NewTaskService(mockRepo)

	taskID := uuid.New()
	existingTask := &models.Task{
		ID:          taskID,
		Title:       "Original Title",
		Description: "Original Description",
		Status:      models.StatusPending,
		Priority:    1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	newTitle := "Updated Title"
	req := &models.UpdateTaskRequest{
		Title: &newTitle,
	}

	mockRepo.On("GetByID", taskID).Return(existingTask, nil)
	mockRepo.On("Update", mock.AnythingOfType("*models.Task")).Return(nil)

	result, err := service.UpdateTask(taskID, req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, newTitle, result.Title)

	mockRepo.AssertExpectations(t)
}

func TestTaskService_DeleteTask(t *testing.T) {
	mockRepo := new(MockTaskRepository)
	service := NewTaskService(mockRepo)

	taskID := uuid.New()

	mockRepo.On("Delete", taskID).Return(nil)

	err := service.DeleteTask(taskID)

	assert.NoError(t, err)

	mockRepo.AssertExpectations(t)
}

