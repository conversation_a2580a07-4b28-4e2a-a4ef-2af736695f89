package services

import (
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/pvnptl/alle-task-service/internal/models"
	"github.com/pvnptl/alle-task-service/internal/repository"
)

// This package contains the business logic layer of the application.
// Services orchestrate operations between handlers and repositories,
// implementing business rules and validation logic.

// TaskService defines the interface for task business operations
type TaskService interface {
	CreateTask(req *models.CreateTaskRequest) (*models.TaskResponse, error)
	GetTaskByID(id uuid.UUID) (*models.TaskResponse, error)
	GetTasks(filter *models.TaskFilter) (*models.TaskListResponse, error)
	UpdateTask(id uuid.UUID, req *models.UpdateTaskRequest) (*models.TaskResponse, error)
	DeleteTask(id uuid.UUID) error
}

// taskService implements TaskService
type taskService struct {
	taskRepo repository.TaskRepository
}

// NewTaskService creates a new task service instance
func NewTaskService(taskRepo repository.TaskRepository) TaskService {
	return &taskService{taskRepo: taskRepo}
}

// CreateTask creates a new task with business logic validation.
// This method demonstrates the service layer's responsibility for:
// 1. Setting default values
// 2. Validating business rules
// 3. Orchestrating data operations
// 4. Transforming data between layers
func (s *taskService) CreateTask(req *models.CreateTaskRequest) (*models.TaskResponse, error) {
	// Set default status if not provided by the client
	// This ensures data consistency and reduces client-side complexity
	if req.Status == "" {
		req.Status = models.StatusPending
	}

	// Create a task entity from the request data
	// This transformation separates API concerns from domain concerns
	task := &models.Task{
		Title:       req.Title,
		Description: req.Description,
		Status:      req.Status,
		Priority:    req.Priority,
		DueDate:     req.DueDate,
	}

	// Validate the task status against business rules
	// This centralized validation ensures consistency across the application
	if !task.IsValidStatus() {
		return nil, errors.New("invalid task status")
	}

	// Persist the task to the database through the repository layer
	// The service doesn't know about database details, maintaining separation of concerns
	if err := s.taskRepo.Create(task); err != nil {
		return nil, err
	}

	// Transform the domain entity back to an API response
	// This ensures the API contract is separate from the internal data model
	return s.taskToResponse(task), nil
}

// GetTaskByID retrieves a task by its ID
func (s *taskService) GetTaskByID(id uuid.UUID) (*models.TaskResponse, error) {
	task, err := s.taskRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	return s.taskToResponse(task), nil
}

// GetTasks retrieves all tasks with filtering and pagination.
// This method demonstrates handling of complex queries with multiple parameters
// and proper pagination calculation for large datasets.
func (s *taskService) GetTasks(filter *models.TaskFilter) (*models.TaskListResponse, error) {
	// Validate and normalize pagination parameters to prevent abuse
	// This ensures consistent behavior and protects against resource exhaustion
	if filter.Page < 1 {
		filter.Page = 1
	}
	if filter.PageSize < 1 || filter.PageSize > 100 {
		filter.PageSize = 10
	}

	// Retrieve tasks from the repository with applied filters
	// The repository handles the complex SQL queries and filtering logic
	tasks, total, err := s.taskRepo.GetAll(filter)
	if err != nil {
		return nil, err
	}

	// Transform domain entities to API responses
	// This maintains separation between internal data models and API contracts
	taskResponses := make([]models.TaskResponse, len(tasks))
	for i, task := range tasks {
		taskResponses[i] = *s.taskToResponse(&task)
	}

	// Calculate pagination metadata for client-side navigation
	// This helps clients implement proper pagination controls
	totalPages := int(math.Ceil(float64(total) / float64(filter.PageSize)))

	return &models.TaskListResponse{
		Tasks: taskResponses,
		Pagination: models.PaginationInfo{
			Page:       filter.Page,
			PageSize:   filter.PageSize,
			Total:      int(total),
			TotalPages: totalPages,
		},
	}, nil
}

// UpdateTask updates an existing task
func (s *taskService) UpdateTask(id uuid.UUID, req *models.UpdateTaskRequest) (*models.TaskResponse, error) {
	// Get existing task
	task, err := s.taskRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Title != nil {
		task.Title = *req.Title
	}
	if req.Description != nil {
		task.Description = *req.Description
	}
	if req.Status != nil {
		tempTask := &models.Task{Status: *req.Status}
		if !tempTask.IsValidStatus() {
			return nil, errors.New("invalid task status")
		}
		task.Status = *req.Status
	}
	if req.Priority != nil {
		task.Priority = *req.Priority
	}
	if req.DueDate != nil {
		task.DueDate = req.DueDate
	}

	if err := s.taskRepo.Update(task); err != nil {
		return nil, err
	}

	return s.taskToResponse(task), nil
}

// DeleteTask deletes a task by its ID
func (s *taskService) DeleteTask(id uuid.UUID) error {
	return s.taskRepo.Delete(id)
}

// taskToResponse converts a Task to TaskResponse
func (s *taskService) taskToResponse(task *models.Task) *models.TaskResponse {
	return &models.TaskResponse{
		ID:          task.ID,
		Title:       task.Title,
		Description: task.Description,
		Status:      task.Status,
		Priority:    task.Priority,
		DueDate:     task.DueDate,
		CreatedAt:   task.CreatedAt,
		UpdatedAt:   task.UpdatedAt,
	}
}
