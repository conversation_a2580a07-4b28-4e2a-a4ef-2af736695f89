package database

import (
	"fmt"
	"log"
	"time"

	"github.com/pvnptl/alle-task-service/internal/config"
	"github.com/pvnptl/alle-task-service/internal/models"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Init initializes the database connection
func Init(cfg *config.Config) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// Configure GORM logger based on environment
	logLevel := logger.Info
	if !cfg.Logging.EnableSQL {
		logLevel = logger.Silent
	}

	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	}

	// Initialize database based on driver
	switch cfg.Database.Driver {
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(cfg.Database.DBName), gormConfig)
	case "postgres":
		// For PostgreSQL, you would use:
		// db, err = gorm.Open(postgres.Open(cfg.GetDatabaseURL()), gormConfig)
		log.Printf("PostgreSQL support not implemented yet, using SQLite for %s environment", cfg.Environment)
		db, err = gorm.Open(sqlite.Open(cfg.Database.DBName), gormConfig)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.Database.Driver)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxOpenConns(cfg.Database.MaxConnections)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.Database.ConnectionTimeout) * time.Second)

	// Auto migrate the schema
	if err := db.AutoMigrate(&models.Task{}); err != nil {
		log.Printf("Failed to migrate database: %v", err)
		return nil, err
	}

	log.Printf("Database initialized successfully for %s environment", cfg.Environment)
	return db, nil
}

