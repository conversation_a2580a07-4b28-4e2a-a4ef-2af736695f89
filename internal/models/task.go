package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// This package contains all data models and DTOs (Data Transfer Objects) for the application.
// It defines the structure of our domain entities and API request/response formats.

// TaskStatus represents the status of a task using a string-based enum pattern.
// This approach provides type safety while maintaining readability and JSON serialization.
type TaskStatus string

// Task status constants define the valid states a task can be in.
// Using constants prevents typos and provides a clear contract for valid status values.
const (
	StatusPending    TaskStatus = "Pending"    // Task is created but not started
	StatusInProgress TaskStatus = "InProgress" // Task is currently being worked on
	StatusCompleted  TaskStatus = "Completed"  // Task has been finished successfully
	StatusCancelled  TaskStatus = "Cancelled"  // Task has been cancelled or abandoned
)

// Task represents a task entity in the system.
// This is the core domain model that defines the structure of a task.
// The struct uses GORM tags for database mapping and Gin binding tags for validation.
type Task struct {
	ID          uuid.UUID  `json:"id" gorm:"type:uuid;primary_key"`                  // Unique identifier using UUID for security
	Title       string     `json:"title" gorm:"not null" binding:"required"`         // Task title - required field
	Description string     `json:"description" gorm:"type:text"`                     // Optional detailed description
	Status      TaskStatus `json:"status" gorm:"type:varchar(20);default:'Pending'"` // Current status with default value
	Priority    int        `json:"priority" gorm:"default:1" binding:"min=1,max=5"`  // Priority level 1-5 with validation
	DueDate     *time.Time `json:"due_date"`                                         // Optional due date (nullable)
	CreatedAt   time.Time  `json:"created_at" gorm:"autoCreateTime"`                 // Auto-managed creation timestamp
	UpdatedAt   time.Time  `json:"updated_at" gorm:"autoUpdateTime"`                 // Auto-managed update timestamp
}

// BeforeCreate is a GORM hook that automatically generates a UUID for new tasks.
// This ensures every task has a unique identifier and prevents ID conflicts.
// The hook is called before inserting a new record into the database.
func (task *Task) BeforeCreate(tx *gorm.DB) error {
	if task.ID == uuid.Nil {
		task.ID = uuid.New()
	}
	return nil
}

// IsValidStatus validates that the task status is one of the predefined valid values.
// This method provides a centralized way to validate task statuses and can be used
// for both API validation and business logic validation.
func (task *Task) IsValidStatus() bool {
	switch task.Status {
	case StatusPending, StatusInProgress, StatusCompleted, StatusCancelled:
		return true
	default:
		return false
	}
}

// TaskResponse represents the response structure for tasks
type TaskResponse struct {
	ID          uuid.UUID  `json:"id"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Status      TaskStatus `json:"status"`
	Priority    int        `json:"priority"`
	DueDate     *time.Time `json:"due_date"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// CreateTaskRequest represents the request structure for creating a task
type CreateTaskRequest struct {
	Title       string     `json:"title" binding:"required"`
	Description string     `json:"description"`
	Status      TaskStatus `json:"status"`
	Priority    int        `json:"priority" binding:"min=1,max=5"`
	DueDate     *time.Time `json:"due_date"`
}

// UpdateTaskRequest represents the request structure for updating a task
type UpdateTaskRequest struct {
	Title       *string     `json:"title"`
	Description *string     `json:"description"`
	Status      *TaskStatus `json:"status"`
	Priority    *int        `json:"priority" binding:"omitempty,min=1,max=5"`
	DueDate     *time.Time  `json:"due_date"`
}

// TaskListResponse represents the response structure for task lists with pagination
type TaskListResponse struct {
	Tasks      []TaskResponse `json:"tasks"`
	Pagination PaginationInfo `json:"pagination"`
}

// PaginationInfo represents pagination metadata
type PaginationInfo struct {
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// TaskFilter represents filtering options for tasks
type TaskFilter struct {
	Status   *TaskStatus `form:"status"`
	Priority *int        `form:"priority"`
	Page     int         `form:"page,default=1"`
	PageSize int         `form:"page_size,default=10"`
}
