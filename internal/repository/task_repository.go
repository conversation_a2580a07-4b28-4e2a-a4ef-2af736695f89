package repository

import (
	"errors"

	"github.com/google/uuid"
	"github.com/pvnptl/alle-task-service/internal/models"
	"gorm.io/gorm"
)

// This package implements the Repository pattern for data access abstraction.
// It provides a clean interface for database operations while hiding the complexity
// of the underlying database implementation (GORM in this case).

// TaskRepository defines the interface for task data operations
type TaskRepository interface {
	Create(task *models.Task) error
	GetByID(id uuid.UUID) (*models.Task, error)
	GetAll(filter *models.TaskFilter) ([]models.Task, int64, error)
	Update(task *models.Task) error
	Delete(id uuid.UUID) error
}

// taskRepository implements TaskRepository
type taskRepository struct {
	db *gorm.DB
}

// NewTaskRepository creates a new task repository instance
func NewTaskRepository(db *gorm.DB) TaskRepository {
	return &taskRepository{db: db}
}

// C<PERSON> creates a new task
func (r *taskRepository) Create(task *models.Task) error {
	return r.db.Create(task).Error
}

// GetByID retrieves a task by its ID
func (r *taskRepository) GetByID(id uuid.UUID) (*models.Task, error) {
	var task models.Task
	if err := r.db.Where("id = ?", id).First(&task).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("task not found")
		}
		return nil, err
	}
	return &task, nil
}

// GetAll retrieves all tasks with filtering and pagination.
// This method demonstrates efficient database querying with dynamic filtering
// and proper pagination implementation for large datasets.
func (r *taskRepository) GetAll(filter *models.TaskFilter) ([]models.Task, int64, error) {
	var tasks []models.Task
	var total int64

	// Start with a base query on the Task model
	// Using Model() ensures we're working with the correct table
	query := r.db.Model(&models.Task{})

	// Apply dynamic filters based on provided parameters
	// This approach allows for flexible querying without multiple method signatures
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.Priority != nil {
		query = query.Where("priority = ?", *filter.Priority)
	}

	// Get total count for pagination metadata
	// This count query should match the filtering criteria for accurate pagination
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination with offset and limit
	// Ordering by created_at DESC ensures newest tasks appear first
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Offset(offset).Limit(filter.PageSize).Order("created_at DESC").Find(&tasks).Error; err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// Update updates an existing task
func (r *taskRepository) Update(task *models.Task) error {
	result := r.db.Save(task)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("task not found")
	}
	return nil
}

// Delete deletes a task by its ID
func (r *taskRepository) Delete(id uuid.UUID) error {
	result := r.db.Where("id = ?", id).Delete(&models.Task{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("task not found")
	}
	return nil
}
