package config

import (
	"fmt"
	"os"
	"strings"
)

// Environment represents the application environment
type Environment string

const (
	EnvironmentLocal   Environment = "local"
	EnvironmentTest    Environment = "test"
	EnvironmentStaging Environment = "staging"
	EnvironmentProd    Environment = "prod"
	EnvironmentDefault Environment = EnvironmentLocal
)

// Config holds all configuration for the application
type Config struct {
	Environment Environment
	Database    DatabaseConfig
	Server      ServerConfig
	Logging     LoggingConfig
	Security    SecurityConfig
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Driver            string
	Host              string
	Port              string
	User              string
	Password          string
	DBName            string
	SSLMode           string
	MaxConnections    int
	ConnectionTimeout int
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port         string
	Mode         string
	ReadTimeout  int
	WriteTimeout int
	IdleTimeout  int
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level     string
	Format    string
	Output    string
	EnableSQL bool
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	CORSOrigins []string
	RateLimit   int
	EnableHTTPS bool
}

// New creates a new configuration instance based on environment
func New() *Config {
	env := getEnvironment()

	config := &Config{
		Environment: env,
		Database:    getDatabaseConfig(env),
		Server:      getServerConfig(env),
		Logging:     getLoggingConfig(env),
		Security:    getSecurityConfig(env),
	}

	return config
}

// getEnvironment determines the current environment
func getEnvironment() Environment {
	env := strings.ToLower(getEnv("ENVIRONMENT", string(EnvironmentDefault)))

	switch Environment(env) {
	case EnvironmentLocal, EnvironmentTest, EnvironmentStaging, EnvironmentProd:
		return Environment(env)
	default:
		fmt.Printf("Warning: Unknown environment '%s', using '%s'\n", env, EnvironmentDefault)
		return EnvironmentDefault
	}
}

// getDatabaseConfig returns database configuration based on environment
func getDatabaseConfig(env Environment) DatabaseConfig {
	switch env {
	case EnvironmentLocal:
		return DatabaseConfig{
			Driver:            getEnv("DB_DRIVER", "sqlite"),
			Host:              getEnv("DB_HOST", "localhost"),
			Port:              getEnv("DB_PORT", "5432"),
			User:              getEnv("DB_USER", ""),
			Password:          getEnv("DB_PASSWORD", ""),
			DBName:            getEnv("DB_NAME", "tasks.db"),
			SSLMode:           getEnv("DB_SSLMODE", "disable"),
			MaxConnections:    getEnvAsInt("DB_MAX_CONNECTIONS", 10),
			ConnectionTimeout: getEnvAsInt("DB_CONNECTION_TIMEOUT", 30),
		}
	case EnvironmentTest:
		return DatabaseConfig{
			Driver:            getEnv("DB_DRIVER", "sqlite"),
			Host:              getEnv("DB_HOST", "localhost"),
			Port:              getEnv("DB_PORT", "5432"),
			User:              getEnv("DB_USER", ""),
			Password:          getEnv("DB_PASSWORD", ""),
			DBName:            getEnv("DB_NAME", "tasks_test.db"),
			SSLMode:           getEnv("DB_SSLMODE", "disable"),
			MaxConnections:    getEnvAsInt("DB_MAX_CONNECTIONS", 5),
			ConnectionTimeout: getEnvAsInt("DB_CONNECTION_TIMEOUT", 10),
		}
	case EnvironmentStaging:
		return DatabaseConfig{
			Driver:            getEnv("DB_DRIVER", "postgres"),
			Host:              getEnv("DB_HOST", "staging-db.example.com"),
			Port:              getEnv("DB_PORT", "5432"),
			User:              getEnv("DB_USER", "staging_user"),
			Password:          getEnv("DB_PASSWORD", ""),
			DBName:            getEnv("DB_NAME", "tasks_staging"),
			SSLMode:           getEnv("DB_SSLMODE", "require"),
			MaxConnections:    getEnvAsInt("DB_MAX_CONNECTIONS", 50),
			ConnectionTimeout: getEnvAsInt("DB_CONNECTION_TIMEOUT", 30),
		}
	case EnvironmentProd:
		return DatabaseConfig{
			Driver:            getEnv("DB_DRIVER", "postgres"),
			Host:              getEnv("DB_HOST", "prod-db.example.com"),
			Port:              getEnv("DB_PORT", "5432"),
			User:              getEnv("DB_USER", "prod_user"),
			Password:          getEnv("DB_PASSWORD", ""),
			DBName:            getEnv("DB_NAME", "tasks_prod"),
			SSLMode:           getEnv("DB_SSLMODE", "require"),
			MaxConnections:    getEnvAsInt("DB_MAX_CONNECTIONS", 100),
			ConnectionTimeout: getEnvAsInt("DB_CONNECTION_TIMEOUT", 30),
		}
	default:
		return DatabaseConfig{}
	}
}

// getServerConfig returns server configuration based on environment
func getServerConfig(env Environment) ServerConfig {
	switch env {
	case EnvironmentLocal:
		return ServerConfig{
			Port:         getEnv("PORT", "8080"),
			Mode:         getEnv("GIN_MODE", "debug"),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 30),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 30),
			IdleTimeout:  getEnvAsInt("SERVER_IDLE_TIMEOUT", 60),
		}
	case EnvironmentTest:
		return ServerConfig{
			Port:         getEnv("PORT", "8081"),
			Mode:         getEnv("GIN_MODE", "test"),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 10),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 10),
			IdleTimeout:  getEnvAsInt("SERVER_IDLE_TIMEOUT", 30),
		}
	case EnvironmentStaging:
		return ServerConfig{
			Port:         getEnv("PORT", "8080"),
			Mode:         getEnv("GIN_MODE", "release"),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 30),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 30),
			IdleTimeout:  getEnvAsInt("SERVER_IDLE_TIMEOUT", 60),
		}
	case EnvironmentProd:
		return ServerConfig{
			Port:         getEnv("PORT", "8080"),
			Mode:         getEnv("GIN_MODE", "release"),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 30),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 30),
			IdleTimeout:  getEnvAsInt("SERVER_IDLE_TIMEOUT", 60),
		}
	default:
		return ServerConfig{}
	}
}

// getLoggingConfig returns logging configuration based on environment
func getLoggingConfig(env Environment) LoggingConfig {
	switch env {
	case EnvironmentLocal:
		return LoggingConfig{
			Level:     getEnv("LOG_LEVEL", "debug"),
			Format:    getEnv("LOG_FORMAT", "text"),
			Output:    getEnv("LOG_OUTPUT", "stdout"),
			EnableSQL: getEnvAsBool("LOG_ENABLE_SQL", true),
		}
	case EnvironmentTest:
		return LoggingConfig{
			Level:     getEnv("LOG_LEVEL", "warn"),
			Format:    getEnv("LOG_FORMAT", "json"),
			Output:    getEnv("LOG_OUTPUT", "stdout"),
			EnableSQL: getEnvAsBool("LOG_ENABLE_SQL", false),
		}
	case EnvironmentStaging:
		return LoggingConfig{
			Level:     getEnv("LOG_LEVEL", "info"),
			Format:    getEnv("LOG_FORMAT", "json"),
			Output:    getEnv("LOG_OUTPUT", "stdout"),
			EnableSQL: getEnvAsBool("LOG_ENABLE_SQL", false),
		}
	case EnvironmentProd:
		return LoggingConfig{
			Level:     getEnv("LOG_LEVEL", "warn"),
			Format:    getEnv("LOG_FORMAT", "json"),
			Output:    getEnv("LOG_OUTPUT", "stdout"),
			EnableSQL: getEnvAsBool("LOG_ENABLE_SQL", false),
		}
	default:
		return LoggingConfig{}
	}
}

// getSecurityConfig returns security configuration based on environment
func getSecurityConfig(env Environment) SecurityConfig {
	switch env {
	case EnvironmentLocal:
		return SecurityConfig{
			CORSOrigins: getEnvAsSlice("CORS_ORIGINS", []string{"*"}),
			RateLimit:   getEnvAsInt("RATE_LIMIT", 1000),
			EnableHTTPS: getEnvAsBool("ENABLE_HTTPS", false),
		}
	case EnvironmentTest:
		return SecurityConfig{
			CORSOrigins: getEnvAsSlice("CORS_ORIGINS", []string{"*"}),
			RateLimit:   getEnvAsInt("RATE_LIMIT", 10000),
			EnableHTTPS: getEnvAsBool("ENABLE_HTTPS", false),
		}
	case EnvironmentStaging:
		return SecurityConfig{
			CORSOrigins: getEnvAsSlice("CORS_ORIGINS", []string{"https://staging.example.com"}),
			RateLimit:   getEnvAsInt("RATE_LIMIT", 100),
			EnableHTTPS: getEnvAsBool("ENABLE_HTTPS", true),
		}
	case EnvironmentProd:
		return SecurityConfig{
			CORSOrigins: getEnvAsSlice("CORS_ORIGINS", []string{"https://example.com", "https://www.example.com"}),
			RateLimit:   getEnvAsInt("RATE_LIMIT", 50),
			EnableHTTPS: getEnvAsBool("ENABLE_HTTPS", true),
		}
	default:
		return SecurityConfig{}
	}
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Environment == EnvironmentProd
}

// IsDevelopment returns true if the environment is local or test
func (c *Config) IsDevelopment() bool {
	return c.Environment == EnvironmentLocal || c.Environment == EnvironmentTest
}

// IsStaging returns true if the environment is staging
func (c *Config) IsStaging() bool {
	return c.Environment == EnvironmentStaging
}

// GetDatabaseURL returns the database connection string
func (c *Config) GetDatabaseURL() string {
	switch c.Database.Driver {
	case "postgres":
		return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
			c.Database.Host, c.Database.Port, c.Database.User, c.Database.Password,
			c.Database.DBName, c.Database.SSLMode)
	case "sqlite":
		return c.Database.DBName
	default:
		return ""
	}
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as integer or returns a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := parseInt(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool gets an environment variable as boolean or returns a default value
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		return strings.ToLower(value) == "true" || value == "1"
	}
	return defaultValue
}

// getEnvAsSlice gets an environment variable as string slice or returns a default value
func getEnvAsSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// parseInt is a helper function to parse string to int
func parseInt(s string) (int, error) {
	var i int
	_, err := fmt.Sscanf(s, "%d", &i)
	return i, err
}
