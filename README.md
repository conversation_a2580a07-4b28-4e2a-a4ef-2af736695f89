# Alle Task Management Service

A microservice-based Task Management System built with Go, demonstrating clean architecture principles, RESTful API design, and microservices concepts.

## 🏗️ Architecture Overview

This project implements a **Task Management Microservice** with clear separation of concerns following the **Single Responsibility Principle**. The architecture is designed to be scalable, maintainable, and follows microservices best practices.

### Design Principles

- **Single Responsibility Principle**: Each component has a single, well-defined responsibility
- **Dependency Injection**: Services depend on interfaces, not concrete implementations
- **Clean Architecture**: Clear separation between layers (Handlers → Services → Repository → Database)
- **RESTful API Design**: Consistent and intuitive API endpoints
- **Horizontal Scalability**: Designed to scale across multiple instances

## 📁 Project Structure

```
alleProject/
├── main.go                          # Application entry point
├── go.mod                           # Go module dependencies
├── Dockerfile                       # Container configuration
├── docker-compose.yml              # Multi-service orchestration
├── Makefile                        # Build and development commands
├── env.example                     # Environment variables template
├── .gitignore                      # Git ignore rules
└── internal/                       # Private application code
    ├── config/                     # Configuration management
    │   └── config.go
    ├── models/                     # Data models and DTOs
    │   └── task.go
    ├── database/                   # Database initialization
    │   └── database.go
    ├── repository/                 # Data access layer
    │   └── task_repository.go
    ├── services/                   # Business logic layer
    │   ├── task_service.go
    │   └── task_service_test.go
    ├── handlers/                   # HTTP request handlers
    │   └── task_handler.go
    └── middleware/                 # HTTP middleware
        └── middleware.go
```

## 🚀 Features

### Core Functionality
- ✅ **CRUD Operations**: Create, Read, Update, Delete tasks
- ✅ **Pagination**: Efficient pagination for large datasets
- ✅ **Filtering**: Filter tasks by status and priority
- ✅ **Validation**: Input validation and error handling
- ✅ **UUID Support**: Secure UUID-based identifiers

### Technical Features
- ✅ **RESTful API**: Standard HTTP methods and status codes
- ✅ **Database Integration**: SQLite with GORM ORM
- ✅ **Environment Configuration**: Flexible configuration management
- ✅ **Docker Support**: Containerized deployment
- ✅ **Health Checks**: Service health monitoring
- ✅ **CORS Support**: Cross-origin resource sharing
- ✅ **Logging**: Request logging and error tracking
- ✅ **Testing**: Unit tests with mocking

## 🛠️ Technology Stack

- **Language**: Go 1.21+
- **Web Framework**: Gin (HTTP router and middleware)
- **Database**: SQLite (with GORM ORM)
- **Containerization**: Docker & Docker Compose
- **Testing**: Testify (assertions and mocking)
- **Configuration**: Environment variables with defaults

## 📋 API Documentation

### Base URL
```
http://localhost:8080/api/v1
```

### Endpoints

#### 1. Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "healthy",
  "service": "task-management-service"
}
```

#### 2. Get All Tasks (with Pagination & Filtering)
```http
GET /tasks?page=1&page_size=10&status=Completed&priority=3
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 10, max: 100)
- `status` (optional): Filter by status (Pending, InProgress, Completed, Cancelled)
- `priority` (optional): Filter by priority (1-5)

**Response:**
```json
{
  "tasks": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "title": "Complete Project Documentation",
      "description": "Write comprehensive documentation for the project",
      "status": "Completed",
      "priority": 3,
      "due_date": "2024-01-15T10:00:00Z",
      "created_at": "2024-01-10T09:00:00Z",
      "updated_at": "2024-01-12T14:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

#### 3. Get Task by ID
```http
GET /tasks/{id}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "Complete Project Documentation",
  "description": "Write comprehensive documentation for the project",
  "status": "Completed",
  "priority": 3,
  "due_date": "2024-01-15T10:00:00Z",
  "created_at": "2024-01-10T09:00:00Z",
  "updated_at": "2024-01-12T14:30:00Z"
}
```

#### 4. Create Task
```http
POST /tasks
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "New Task",
  "description": "Task description",
  "status": "Pending",
  "priority": 2,
  "due_date": "2024-01-20T10:00:00Z"
}
```

**Response:** `201 Created` with the created task

#### 5. Update Task
```http
PUT /tasks/{id}
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Updated Task Title",
  "status": "InProgress",
  "priority": 4
}
```

**Response:** `200 OK` with the updated task

#### 6. Delete Task
```http
DELETE /tasks/{id}
```

**Response:**
```json
{
  "message": "Task deleted successfully"
}
```

### Task Status Values
- `Pending`: Task is created but not started
- `InProgress`: Task is currently being worked on
- `Completed`: Task has been finished
- `Cancelled`: Task has been cancelled

### Priority Levels
- `1`: Lowest priority
- `2`: Low priority
- `3`: Medium priority
- `4`: High priority
- `5`: Highest priority

## 🚀 Getting Started

### Prerequisites
- Go 1.21 or higher
- Docker (optional, for containerized deployment)

### Local Development

1. **Clone the repository**
```bash
git clone <repository-url>
cd alleProject
```

2. **Install dependencies**
```bash
go mod download
go mod tidy
```

3. **Set up environment variables**
```bash
cp env.example .env
# Edit .env file if needed
```

4. **Run the application**
```bash
# Using Go directly
go run main.go

# Using Makefile
make run

# Using hot reload (requires gin)
make dev
```

5. **Test the API**
```bash
# Health check
curl http://localhost:8080/health

# Create a task
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Task",
    "description": "This is a test task",
    "priority": 3
  }'
```

### Docker Deployment

1. **Build and run with Docker**
```bash
# Build the image
docker build -t task-service .

# Run the container
docker run -p 8080:8080 task-service
```

2. **Using Docker Compose**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f task-service

# Stop services
docker-compose down
```

### Testing

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run specific test file
go test ./internal/services/ -v
```

## 🔧 Configuration

The application can be configured using environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8080` | Server port |
| `GIN_MODE` | `debug` | Gin framework mode |
| `DB_DRIVER` | `sqlite` | Database driver |
| `DB_NAME` | `tasks.db` | Database name |
| `DB_HOST` | `localhost` | Database host |
| `DB_PORT` | `5432` | Database port |
| `DB_USER` | `` | Database username |
| `DB_PASSWORD` | `` | Database password |
| `DB_SSLMODE` | `disable` | Database SSL mode |

## 🏗️ Microservices Architecture

### Current Service
This Task Management Service is designed as a standalone microservice that can operate independently.

### Horizontal Scaling
The service can be scaled horizontally by:
1. **Load Balancing**: Multiple instances behind a load balancer
2. **Stateless Design**: No shared state between instances
3. **Database Scaling**: Using read replicas or sharding
4. **Container Orchestration**: Kubernetes or Docker Swarm deployment

### Inter-Service Communication
When adding additional microservices (e.g., User Service), communication can be implemented using:

#### 1. REST API (Recommended for this project)
```go
// Example: Task service calling User service
func (s *taskService) CreateTaskWithUser(req *CreateTaskRequest, userID string) error {
    // Call User service via HTTP
    user, err := s.userClient.GetUser(userID)
    if err != nil {
        return err
    }
    // Continue with task creation
}
```

#### 2. gRPC (For high-performance communication)
```protobuf
// Example: task_service.proto
service TaskService {
    rpc CreateTask(CreateTaskRequest) returns (TaskResponse);
    rpc GetTask(GetTaskRequest) returns (TaskResponse);
}
```

#### 3. Message Queues (For asynchronous communication)
```go
// Example: Publishing task events
func (s *taskService) CreateTask(req *CreateTaskRequest) error {
    // Create task
    task, err := s.taskRepo.Create(req)
    if err != nil {
        return err
    }
    
    // Publish event to message queue
    s.eventPublisher.Publish("task.created", task)
    return nil
}
```

### Service Discovery
For production deployments, implement service discovery using:
- **Consul**: Service registration and health checks
- **etcd**: Distributed key-value store
- **Kubernetes**: Built-in service discovery

## 🔒 Security Considerations

1. **Input Validation**: All inputs are validated using Gin's binding
2. **SQL Injection Prevention**: Using GORM with parameterized queries
3. **CORS Configuration**: Proper CORS headers for web applications
4. **Error Handling**: Generic error messages to prevent information leakage
5. **UUID Usage**: Secure UUID-based identifiers instead of sequential IDs

## 📊 Monitoring and Observability

### Health Checks
- Endpoint: `GET /health`
- Returns service status and basic information

### Logging
- Request logging with timestamps and response codes
- Error logging with stack traces
- Structured logging for production environments

### Metrics (Future Enhancement)
```go
// Example: Prometheus metrics
var (
    taskCreationCounter = prometheus.NewCounter(prometheus.CounterOpts{
        Name: "tasks_created_total",
        Help: "Total number of tasks created",
    })
)
```

## 🧪 Testing Strategy

### Unit Tests
- Service layer testing with mocked repositories
- Handler testing with mocked services
- Model validation testing

### Integration Tests
- Database integration tests
- API endpoint testing
- End-to-end workflow testing

### Test Coverage
```bash
# Run tests with coverage
make test-coverage

# View coverage report
go tool cover -html=coverage.out
```

## 🚀 Deployment

### Production Considerations
1. **Environment Variables**: Use proper environment management
2. **Database**: Use production-grade database (PostgreSQL/MySQL)
3. **Logging**: Implement structured logging
4. **Monitoring**: Add metrics and alerting
5. **Security**: Implement authentication and authorization
6. **SSL/TLS**: Use HTTPS in production

### Kubernetes Deployment
```yaml
# Example: task-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: task-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: task-service
  template:
    metadata:
      labels:
        app: task-service
    spec:
      containers:
      - name: task-service
        image: task-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For questions or issues, please create an issue in the repository or contact the development team.

---

**Note**: This is a demonstration project showcasing microservices architecture principles. For production use, additional considerations such as authentication, authorization, rate limiting, and comprehensive monitoring should be implemented.

