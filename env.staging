# Staging Environment
ENVIRONMENT=staging

# Server Configuration
PORT=8080
GIN_MODE=release
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30
SERVER_IDLE_TIMEOUT=60

# Database Configuration
DB_DRIVER=postgres
DB_HOST=staging-db.example.com
DB_PORT=5432
DB_USER=staging_user
DB_PASSWORD=your_staging_password_here
DB_NAME=tasks_staging
DB_SSLMODE=require
DB_MAX_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_ENABLE_SQL=false

# Security Configuration
CORS_ORIGINS=https://staging.example.com
RATE_LIMIT=100
ENABLE_HTTPS=true
